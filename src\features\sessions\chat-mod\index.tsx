import { HoldMessageModal } from "@/components/hold-message-modal";
import { ConfirmDialog } from "@/components/modal";
import { ProblemModal } from "@/components/problem-modal";
import { useAuthStore } from "@/stores/authStore";
import useChatStore from "@/stores/useChatStore";
import { useParams } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { useGetConversationDetails, useGetConversationProfile } from "../api";
import CenterChatPanel from "./center-chat-panel";
import LeftPanel from "./left-panel";
import RightPanel from "./right-panel";

export default function ChatProfileView() {
  const { conversationId } = useParams({
    from: "/_authenticated/sessions/chat-mod/$conversationId",
  });
  const [show, setShow] = useState(false);
  const [showHold, setShowHold] = useState(false);
  const [showProblem, setShowProblem] = useState(false);

  const { initializeSocket } = useChatStore();
  const { auth } = useAuthStore();

  const currentUserId = auth.user?.accountNo
    ? parseInt(auth.user.accountNo, 10)
    : 0;

  const conversationIdNum = parseInt(conversationId, 10);

  const {
    data: { customer = {}, model = {} } = {},
    isLoading: isProfileLoading,
    isError: isProfileError,
  } = useGetConversationProfile(conversationId);

  const {
    data: {
      problems = [],
      customerInfo = {},
      modelInfo = {},
      modelNotes = [],
      customerNotes = [],
    } = {},
  } = useGetConversationDetails(conversationId);

  useEffect(() => {
    const cleanup = initializeSocket([]);
    return cleanup;
  }, [initializeSocket]);

  const isNoDataFound = !customer?.id || !model?.id;

  if (isProfileLoading) {
    return (
      <div className="text-center text-gray-500 text-sm py-8 h-full flex items-center justify-center">
        Loading...
      </div>
    );
  }

  if (isProfileError) {
    return (
      <div className="text-center text-red-500 text-sm py-8 h-full flex items-center justify-center">
        Something went wrong. Please try again.
      </div>
    );
  }

  if (isNoDataFound) {
    return (
      <div className="text-center text-gray-500 text-sm py-8 h-full flex items-center justify-center">
        No data found. Try again.
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 xl:grid-cols-[1fr_2fr_1fr] h-screen w-full gap-[16px] px-3 py-4 sm:px-4 sm:py-6">
      <LeftPanel
        data={model}
        conversationId={conversationIdNum}
        modelInfo={modelInfo}
        notesApiData={modelNotes}
      />

      <CenterChatPanel
        setShowHold={setShowHold}
        setShowProblem={setShowProblem}
        conversationId={conversationIdNum}
        currentUserId={currentUserId}
        modelInfo={model}
        customerInfo={customer}
      />

      <RightPanel
        data={customer}
        setShow={setShow}
        conversationId={conversationIdNum}
        customerInfo={customerInfo}
        notesApiData={customerNotes}
      />

      <ProblemModal
        open={showProblem}
        onOpenChange={setShowProblem}
        problems={problems}
        payloadData={{
          conversationId: conversationIdNum,
          userId: customer?.id,
        }}
      />

      <HoldMessageModal
        open={showHold}
        onOpenChange={setShowHold}
        isLoading={false}
        onSave={() => {}}
      />

      <ConfirmDialog open={show} setOpen={setShow} data={customer} />
    </div>
  );
}
