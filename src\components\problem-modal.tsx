import {
  <PERSON>ertDialog,
  AlertDialog<PERSON><PERSON><PERSON>,
  AlertDialog<PERSON><PERSON>nt,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { useSaveConversationProblem } from "@/features/sessions/api";
import { useState } from "react";
import { toast } from "sonner";
import { MAX_TEXTAREA_LENGTH } from "@/utils/constant";

interface Problem {
  id: string;
  title: string;
}
interface ProblemModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  problems: Problem[];
  payloadData: any;
}

export function ProblemModal({
  open,
  onOpenChange,
  problems,
  payloadData,
}: ProblemModalProps) {
  const [selected, setSelected] = useState<string | null>(null);
  const [comment, setComment] = useState<string>("");
  const { mutateAsync: saveConversationProblem } = useSaveConversationProblem();

  const handleSave = async (selected: string | null) => {
    try {
      const payload: any = {
        ...payloadData,
        problemId: selected,
      };

      if (comment) {
        payload.comment = comment;
      }

      const response: any = await saveConversationProblem(payload);
      if (response?.success) {
        toast.success("Problem saved successfully!");
        onOpenChange(false);
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent width="max-w-xl" className="p-0 rounded-xl bg-card">
        <AlertDialogHeader className="px-6 pt-6 pb-2 text-left">
          <AlertDialogTitle className="text-2xl font-semibold">
            Problem
          </AlertDialogTitle>
        </AlertDialogHeader>
        <div className="px-6">
          {/* Problem selection buttons */}
          <div className="grid grid-cols-3 gap-3 mb-4">
            {problems.map((problem) => (
              <button
                key={problem?.id}
                type="button"
                className={cn(
                  "border rounded-lg p-3 text-sm text-center transition-colors",
                  selected === problem?.id
                    ? "bg-primary text-primary-foreground border-primary"
                    : "bg-card text-card-foreground border-border hover:bg-accent hover:text-accent-foreground"
                )}
                onClick={() => setSelected(problem?.id)}
              >
                {problem?.title}
              </button>
            ))}
          </div>

          <Textarea
            placeholder="Add a comment (optional)"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            className="mb-4 resize-none min-h-[120px]"
            rows={6}
            maxLength={MAX_TEXTAREA_LENGTH}
          />
          <div className="text-xs text-muted-foreground text-right">
            {comment.length}/{MAX_TEXTAREA_LENGTH} characters
          </div>
        </div>
        <AlertDialogFooter className="px-6 pb-6 pt-2 flex-row-reverse gap-2">
          <AlertDialogCancel className="min-w-[120px] border-gray-300 bg-transparent cursor-pointer">
            Cancel
          </AlertDialogCancel>
          <Button
            onClick={() => handleSave(selected)}
            disabled={!selected}
            className="min-w-[120px] bg-[#171717] cursor-pointer"
          >
            Save
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
