import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { calculateAge, FormatS3ImgUrl, generateNickName } from "@/utils/common";
import { Avatar, AvatarFallback, AvatarImage } from "@radix-ui/react-avatar";
import { useEffect, useState } from "react";
import {
  useDeleteConversationNote,
  useSaveConversationNote,
  useUpdateUserConversationInfo,
} from "../api";
import { InformationSection } from "./components/information-section";
import { NotesSection } from "./components/notes-section";
import {
  createFieldsFromApiData,
  createFieldValuesFromApiData,
  getFieldConfigByType,
} from "./field-config";

import { FieldValues, InformationFormData, NotesData } from "./types";

export default function LeftPanel({
  data,
  conversationId,
  modelInfo,
  notesApiData,
}: any) {
  const { mutateAsync: updateUserConversationInfo } =
    useUpdateUserConversationInfo();
  const { mutateAsync: saveConversationNote } = useSaveConversationNote();
  const { mutateAsync: deleteConversationNote } = useDeleteConversationNote();

  const [notesData, setNotesData] = useState<NotesData>({
    notes: [],
    currentNote: "",
  });
  const [informationFields, setInformationFields] = useState(() => {
    return createFieldsFromApiData(modelInfo);
  });
  const [fieldValues, setFieldValues] = useState<FieldValues>(() => {
    return createFieldValuesFromApiData(modelInfo);
  });
  const [isNotesLoading, setIsNotesLoading] = useState(false);

  useEffect(() => {
    if (notesApiData) {
      setNotesData({
        notes: notesApiData,
        currentNote: "",
      });
    }
  }, [notesApiData]);

  const handleAddNote = async (note: string) => {
    try {
      setIsNotesLoading(true);
      const result = await saveConversationNote({
        userId: data?.id,
        conversationId,
        note,
      });
      if (result?.success) {
        setNotesData((prev) => ({
          ...prev,
          notes: [result?.data?.notes, ...prev.notes],
          currentNote: "",
        }));
      }
    } catch (error) {
      console.error("Error saving note:", error);
    } finally {
      setIsNotesLoading(false);
    }
  };

  const handleDeleteNote = async (noteId: string) => {
    try {
      const result: any = await deleteConversationNote(noteId);
      if (result?.success) {
        setNotesData((prev) => ({
          ...prev,
          notes: prev.notes.filter((note) => note.id !== noteId),
        }));
      }
    } catch (error) {
      console.error("Error deleting note:", error);
    }
  };

  const handleUpdateField = async (
    fieldId: string,
    formData: InformationFormData
  ) => {
    try {
      const fieldConfig = getFieldConfigByType(formData.type);
      if (!fieldConfig) return;

      const result: any = await updateUserConversationInfo({
        userId: data?.id,
        conversationId,
        [fieldConfig.apiKey]: formData.content,
      });

      if (result?.success) {
        setInformationFields((prev) =>
          prev.map((field) => {
            if (field.id === fieldConfig.id) {
              return {
                ...field,
                type: formData.type,
                items: formData.content,
              };
            }
            return field;
          })
        );

        setFieldValues((prev) => ({
          ...prev,
          [fieldConfig.id]: formData.content,
        }));
      }
    } catch (error) {
      console.error("Error updating field:", error);
    }
  };

  return (
    <div className="flex flex-col gap-[16px] mb-5">
      <div className="bg-sidebar p-4 rounded-2xl">
        <Avatar className="w-[54px] h-[54px] mb-[12px] rounded-full bg-[#999] text-white flex items-center justify-center">
          {data?.avatar ? (
            <AvatarImage
              src={FormatS3ImgUrl(data?.avatar)}
              alt="Avatar"
              className="object-cover rounded-full w-full h-full"
            />
          ) : (
            <AvatarFallback className="text-lg">
              {generateNickName(data?.username)}
            </AvatarFallback>
          )}
        </Avatar>
        <div className="text-lg font-medium">
          {data?.username} ({calculateAge(data?.dob)})
        </div>
        <div className="text-sm">{data?.city} - VTL: 0</div>
        <div className="text-sm">Timezone: GMT+5:30</div>
        <hr className="my-3"></hr>
        <div className="flex flex-wrap">
          <div className="basis-1/2">
            <div className="text-sm">Relation Status</div>
            <div className="text-sm font-semibold">
              {data?.relationshipStatus?.title}
            </div>
          </div>
          <div className="basis-1/2">
            <div className="text-sm">Location</div>
            <div className="text-sm font-semibold">{data?.country?.name}</div>
          </div>
          <div className="basis-2/2 mt-3">
            <div className="text-sm">Interests</div>
            <div className="text-sm font-semibold">
              {data.interest?.length > 0
                ? data?.interest?.map((item: any) => item.title).join(", ")
                : "No Interests"}
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className="text-sm font-semibold mb-[10px]">Affiliate</div>
        <div className="">
          <Select>
            <SelectTrigger className="w-full !h-12 rounded-xl border border-gray-300 bg-white px-4 text-sm focus:ring-0 focus:outline-none">
              <SelectValue placeholder="Select option" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              <SelectItem value="yes">Yes</SelectItem>
              <SelectItem value="no">No</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <NotesSection
        data={notesData}
        onAddNote={handleAddNote}
        onDeleteNote={handleDeleteNote}
        isLoading={isNotesLoading}
        title={`Notes History for ${data?.username}`}
      />

      <InformationSection
        fields={informationFields}
        onUpdateField={handleUpdateField}
        fieldValues={fieldValues}
        onFieldValuesChange={setFieldValues}
      />
    </div>
  );
}
