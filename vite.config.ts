import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { defineConfig, loadEnv } from "vite";
import { TanStackRouterVite } from "@tanstack/router-plugin/vite";

const env = loadEnv("env", process.cwd());

export default defineConfig({
  plugins: [
    TanStackRouterVite({ target: "react", autoCodeSplitting: true }),
    react(),
    tailwindcss(),
  ],
  optimizeDeps: {
    include: ['slate', 'slate-react', 'slate-history'],
  },
  css: {
    devSourcemap: true,
  },
  server: {
    port: 3002,
    open: true,
    watch: {
      usePolling: true,
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    outDir: "./build",
    chunkSizeWarningLimit: 1600,
  },
  define: {
    global: 'window', // <- This makes fbjs think global is window
  },
  base: env.VITE_BASE_URL,
});
