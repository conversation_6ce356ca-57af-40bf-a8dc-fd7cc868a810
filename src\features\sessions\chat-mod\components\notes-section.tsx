import { But<PERSON> } from "@/components/ui/button";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { DotsVerticalIcon } from "@radix-ui/react-icons";
import { IconHistory, IconTrash } from "@tabler/icons-react";
import React, { useState } from "react";
import { NotesComponentProps, NotesHistoryModalProps } from "../types";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

function NotesHistoryModal({
  open,
  onOpenChange,
  notes,
  onDeleteNote,
  isDeleting,
  title = "Notes History",
}: NotesHistoryModalProps) {
  const handleDeleteNote = (noteId: string) => {
    onDeleteNote(noteId);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[400px] w-full">
          <div className="space-y-3 pr-4">
            {notes.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                No notes found
              </div>
            ) : (
              notes.map((note) => (
                <div
                  key={note.id}
                  className="bg-sidebar-accent p-3 rounded-lg w-[400px] max-w-[400px] overflow-hidden"
                >
                  <div className="flex justify-between items-start mb-2">
                    <TooltipProvider delayDuration={200}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="text-sm flex-1 overflow-hidden text-ellipsis whitespace-nowrap cursor-default">
                            {note.note}
                          </div>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-sm break-words">
                          {note.note}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>

                  <hr className="my-3" />
                  <div className="flex gap-4 items-center justify-between">
                    <div className="text-xs">
                      Created:{"  "}
                      {new Date(note.createdAt).toLocaleDateString("en-US", {
                        month: "short",
                        day: "numeric",
                        year: "numeric",
                      })}
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled={isDeleting}
                          className="h-6 w-6 p-0 ml-2 hover:bg-gray-100 cursor-pointer"
                        >
                          <DotsVerticalIcon className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[120px]">
                        <DropdownMenuItem
                          onClick={() => handleDeleteNote(note.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <IconTrash size={14} className="mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}

export function NotesSection({
  data,
  onAddNote,
  onDeleteNote,
  isLoading,
  title,
}: NotesComponentProps) {
  const [currentNote, setCurrentNote] = useState(data.currentNote || "");
  const [showHistory, setShowHistory] = useState(false);
  const MAX_NOTE_LENGTH = 500;

  const handleAddNote = () => {
    if (currentNote.trim()) {
      onAddNote(currentNote.trim());
      setCurrentNote("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleAddNote();
    }
  };

  const handleDeleteNote = (noteId: string) => {
    onDeleteNote(noteId);
  };

  return (
    <>
      <div className="bg-sidebar p-4 rounded-2xl">
        <div className="flex justify-between items-center mb-[12px]">
          <div className="text-base font-semibold">Notes</div>
          {data?.notes?.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHistory(true)}
              className="h-8 px-3 cursor-pointer"
            >
              <IconHistory width="16px" className="mr-1" />
              View History
            </Button>
          )}
        </div>

        <div className="flex mb-4 overflow-x-auto space-x-3 flex-nowrap">
          {data.notes.slice(0, 2).map((note) => (
            <div
              key={note.id}
              className="bg-sidebar-accent p-3 rounded-lg w-[150px] max-w-[150px]"
            >
              <div className="flex justify-between items-start mb-2">
                <TooltipProvider delayDuration={200}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="text-sm flex-1 overflow-hidden text-ellipsis whitespace-nowrap cursor-default">
                        {note.note}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-sm break-words">
                      {note.note}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <hr className="my-3" />
              <div className="flex gap-4 items-center justify-between">
                <div className="text-xs">
                  Created: <br />
                  {new Date(note.createdAt).toLocaleDateString("en-US", {
                    month: "short",
                    day: "numeric",
                    year: "numeric",
                  })}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      disabled={isLoading}
                      className="h-6 w-6 p-0 ml-2 hover:bg-gray-100 cursor-pointer"
                    >
                      <DotsVerticalIcon className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[120px]">
                    <DropdownMenuItem
                      onClick={() => handleDeleteNote(note.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <IconTrash size={14} className="mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
          {data.notes.length === 0 && (
            <div className="p-3 rounded-lg min-w-[200px] text-center text-muted-foreground w-full">
              No notes yet
            </div>
          )}
        </div>

        <div className="space-y-2">
          <div className="flex gap-2">
            <Input
              type="text"
              placeholder="Enter Note"
              className="text-sm h-[42px] shadow-none flex-1"
              value={currentNote}
              onChange={(e) => {
                if (e.target.value.length <= MAX_NOTE_LENGTH) {
                  setCurrentNote(e.target.value);
                }
              }}
              onKeyDown={handleKeyDown}
              maxLength={MAX_NOTE_LENGTH}
            />
            <Button
              onClick={handleAddNote}
              disabled={!currentNote.trim() || isLoading}
              size="sm"
              className="h-[42px] px-4"
            >
              {isLoading ? "Adding..." : "Add"}
            </Button>
          </div>
          <div className="text-xs text-muted-foreground text-right">
            {currentNote.length}/{MAX_NOTE_LENGTH} characters
          </div>
        </div>
      </div>

      <NotesHistoryModal
        open={showHistory}
        onOpenChange={setShowHistory}
        notes={data.notes}
        onDeleteNote={handleDeleteNote}
        isDeleting={isLoading}
        title={title}
      />
    </>
  );
}
