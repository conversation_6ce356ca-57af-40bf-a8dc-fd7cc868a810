import { create } from "zustand";
import io from "socket.io-client";
import { useAuthStore, getAccessToken } from "./authStore";

interface User {
  id: number;
  name: string | null;
  username: string;
  email: string | null;
  city: string;
  avatar: string | null;
  isOnline: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ChatUser {
  id: number;
  name: string;
  location: string;
  avatar?: string;
}

interface ApiMessage {
  id: number;
  sender: User;
  senderId: number;
  conversationId: number;
  message: string;
  messageType: "text" | "image" | "file";
  messageStatus: "sent" | "delivered" | "read";
  isRead: boolean;
  metadata: unknown;
  createdAt: string;
  updatedAt: string;
}
interface ModeratorMessage {
  id: number;
  senderId: number;
  replyToId: number | null;
  moderatorId: number | null;
  conversationId: number;
  message: string;
  messageType: "text" | "image" | "file";
  messageStatus: "sent" | "delivered" | "read";
  isRead: boolean;
  metadata: unknown;
  createdAt: string;
  updatedAt: string;
}

interface Message {
  id: number;
  senderId: number;
  receiverId: number;
  message: string;
  messageType: "text" | "image" | "file";
  timestamp: string;
  status: "sent" | "delivered" | "read";
  conversationId: number;
  sender?: "me" | "other";
}

interface ChatState {
  activeChatUser: ChatUser | null;
  messages: Record<number, Message[]>;
  socket: any;
  conversations: unknown[];
  isConnected: boolean;
  currentUserId: number | null;
  setConversations: (conversations: unknown[]) => void;
  moderatorMessages: Record<number, ModeratorMessage[]>;
  moderatorMessagesMeta: Record<
    number,
    {
      total: number;
      page: number;
      limit: number;
      pages: number;
      search: string | null;
    }
  >;
  isLoadingMoreMessages: boolean;

  initializeSocket: (domainList: Array<string>) => () => void;
  disconnectSocket: () => void;
  setMessages: (userId: number, messages: Message[]) => void;

  "mod-sendMessage": (messageData: {
    message: string;
    receiverId: number;
    senderId: number;
    conversationId: number;
    messageType?: "text" | "image" | "file";
  }) => void;
  "mod-setMessages": (
    conversationId: number,
    messages: ModeratorMessage[],
    meta: unknown,
    isLoadingMore?: boolean
  ) => void;
  "mod-addMessage": (conversationId: number, message: ModeratorMessage) => void;
  "mod-setLoadingMore": (isLoading: boolean) => void;
  "mod-appendOlderMessages": (
    conversationId: number,
    olderMessages: ModeratorMessage[],
    meta: unknown
  ) => void;

  transformApiMessage: (
    apiMessage: ApiMessage,
    currentUserId: number
  ) => Message;
  processApiMessages: (
    apiMessages: ApiMessage[],
    currentUserId: number
  ) => Message[];
  addRoomJoined: () => boolean;
}

const useChatStore = create<ChatState>((set, get) => ({
  activeChatUser: null,
  messages: {},
  conversations: [],
  socket: null,
  isConnected: false,
  currentUserId: null,
  moderatorMessages: {},
  moderatorMessagesMeta: {},
  isLoadingMoreMessages: false,
  transformApiMessage: (
    apiMessage: ApiMessage,
    currentUserId: number
  ): Message => {
    return {
      id: apiMessage.id,
      senderId: apiMessage.senderId,
      receiverId:
        currentUserId === apiMessage.senderId
          ? apiMessage.conversationId
            ? 0
            : currentUserId
          : currentUserId,
      message: apiMessage.message,
      messageType: apiMessage.messageType,
      timestamp: apiMessage.createdAt,
      status: apiMessage.messageStatus,
      conversationId: apiMessage.conversationId,
      sender: apiMessage.senderId === currentUserId ? "me" : "other",
    };
  },

  processApiMessages: (
    apiMessages: ApiMessage[],
    currentUserId: number
  ): Message[] => {
    return apiMessages.map((apiMessage) =>
      get().transformApiMessage(apiMessage, currentUserId)
    );
  },

  initializeSocket: (domainList?: Array<string>) => {
    const token = getAccessToken();
    const authUser: any = useAuthStore.getState().auth.user;
    const userId = authUser?.id ? parseInt(authUser.id, 10) : null;

    if (!token || !userId) {
      console.error("Cannot initialize socket: missing token or userId", {
        token: !!token,
        userId,
      });
      return () => {};
    }

    set({ currentUserId: userId });
    const socketUrl = import.meta.env.VITE_SOCKET_URL;

    if (!socketUrl) {
      console.error("Socket URL not configured in environment variables");
      return () => {};
    }

    const socket = io(socketUrl, {
      auth: {
        token: "customer_token",
        userId: parseInt(userId.toString()),
      },
      transports: ["websocket", "polling"],
      timeout: 20000,
    });

    socket.on("connect", () => {
      console.log("Socket connected successfully");
      set({ isConnected: true, socket });
    });

    socket.on("disconnect", () => {
      console.log("Socket disconnected:");
      set({ isConnected: false });
    });

    socket.on("direct_message_received", (response) => {
      get()["mod-addMessage"](
        response?.message?.conversationId,
        response?.message
      );
    });

    socket.on("connect_error", (error) => {
      console.error("Socket connection error:", error);
      set({ isConnected: false });
    });

    socket.on("reconnect", () => {
      set({ isConnected: true });
    });

    socket.on("reconnect_error", (error) => {
      console.error("Socket reconnection error:", error);
    });

    socket.on("moderator_message_event", (response) => {
      console.log("moderator_message_event", response);
      useChatStore.getState().setConversations(response.session);
    });

    if (domainList?.length) {
      for (const domain of domainList) {
        socket.emit("join_domain_room", { domainName: domain });
      }
    }

    return () => {
      socket.disconnect();
      set({ socket: null, isConnected: false });
    };
  },

  disconnectSocket: () => {
    const { socket } = get();
    if (socket) {
      socket.disconnect();
      set({ socket: null, isConnected: false });
    }
  },

  setMessages: (userId, messages) => {
    set((state) => ({
      messages: {
        ...state.messages,
        [userId]: messages,
      },
    }));
  },

  setConversations: (conversations) => {
    console.log("settled");
    set({ conversations });
  },

  addRoomJoined: () => {
    const { socket } = get();
    socket.emit("join_domain_room", { domainName: "xyz.website.com" });
    return true;
  },

  "mod-sendMessage": ({
    message,
    receiverId,
    senderId,
    messageType = "text",
    conversationId,
  }: any) => {
    const { socket, isConnected, currentUserId } = get();

    if (!socket || !isConnected || !currentUserId) {
      console.error(
        "Cannot send moderator message: Socket not connected or user not authenticated"
      );
      return;
    }

    try {
      const messageData = {
        receiverId,
        senderId,
        message,
        messageType,
      };

      socket.emit("send_chat_message", messageData);

      const newMessage = {
        id: Date.now(), // Temporary ID until server responds
        senderId,
        replyToId: null,
        moderatorId: currentUserId,
        conversationId,
        message,
        messageType: "text" as const,
        messageStatus: "sent" as const,
        isRead: false,
        metadata: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      console.log("Adding new message:", newMessage);
      get()["mod-addMessage"](conversationId, newMessage);
    } catch (error) {
      console.error("Error sending moderator message:", error);
    }
  },

  "mod-setMessages": (
    conversationId: number,
    messages: ModeratorMessage[],
    meta: any,
    isLoadingMore = false
  ) => {
    set((state) => {
      const existingMessages = state.moderatorMessages[conversationId] || [];
      const newMessages = isLoadingMore
        ? [...messages, ...existingMessages]
        : messages;

      return {
        moderatorMessages: {
          ...state.moderatorMessages,
          [conversationId]: newMessages,
        },
        moderatorMessagesMeta: {
          ...state.moderatorMessagesMeta,
          [conversationId]: meta,
        },
        isLoadingMoreMessages: false,
      };
    });
  },

  "mod-addMessage": (conversationId: number, message: ModeratorMessage) => {
    set((state) => ({
      moderatorMessages: {
        ...state.moderatorMessages,
        [conversationId]: [
          ...(state.moderatorMessages[conversationId] || []),
          message,
        ],
      },
    }));
  },

  "mod-setLoadingMore": (isLoading: boolean) => {
    set({ isLoadingMoreMessages: isLoading });
  },

  "mod-appendOlderMessages": (
    conversationId: number,
    olderMessages: ModeratorMessage[],
    meta: any
  ) => {
    set((state) => {
      const existingMessages = state.moderatorMessages[conversationId] || [];
      // Prepend older messages (they come first chronologically)
      const mergedMessages = [...olderMessages, ...existingMessages];

      // Remove duplicates based on message ID
      const uniqueMessages = mergedMessages.filter(
        (message, index, array) =>
          array.findIndex((m) => m.id === message.id) === index
      );

      return {
        moderatorMessages: {
          ...state.moderatorMessages,
          [conversationId]: uniqueMessages,
        },
        moderatorMessagesMeta: {
          ...state.moderatorMessagesMeta,
          [conversationId]: meta,
        },
        isLoadingMoreMessages: false,
      };
    });
  },
}));

export const cleanupChatSocket = () => {
  const { disconnectSocket } = useChatStore.getState();
  disconnectSocket();
};

export const initializeChatSocket = () => {
  const { initializeSocket } = useChatStore.getState();
  initializeSocket();
};

export default useChatStore;
